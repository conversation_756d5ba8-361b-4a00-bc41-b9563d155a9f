# Krabulon Environment Configuration
# Copy this file to .env and update with your values

# Required - OpenAI API Configuration
OPENAI_API_KEY=your-openai-api-key-here

# Required - Security
SECRET_KEY=your-secret-key-change-in-production

# Database Configuration - PostgreSQL
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=hvac_crm
DATABASE_USER=postgres
DATABASE_PASSWORD=password

# Database Configuration - MongoDB
MONGODB_HOST=localhost
MONGODB_PORT=27017
MONGODB_DATABASE=hvac_equipment
MONGODB_USERNAME=
MONGODB_PASSWORD=

# Database Configuration - Neo4j
NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=password

# Database Configuration - Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DATABASE=0
REDIS_PASSWORD=

# Application Configuration
APP_HOST=0.0.0.0
APP_PORT=8000
APP_DEBUG=false

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/krabulon.log

# Crawling Configuration
CRAWLING_MAX_PAGES=50
CRAWLING_DELAY=1.0
CRAWLING_TIMEOUT=30
CRAWLING_USER_AGENT=Krabulon HVAC Bot 1.0

# Monitoring Configuration
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090

# Development Configuration
RELOAD=false
WORKERS=1
